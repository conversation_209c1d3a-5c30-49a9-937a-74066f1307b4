import React, { useState, useEffect } from 'react';
import { Button, Select, Space, Spin, Collapse } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { FileData, ComponentType } from '../Views Section/types';
import { useNavigate } from 'react-router-dom';

interface BatchExplorationSidebarProps {
  files?: FileData[];
  selectedFile?: FileData | null;
  onFileSelect?: (file: FileData) => void;
  onBackClick?: () => void;
  activePanels?: ComponentType[];
  isLoading?: boolean;
}

const BatchExplorationSidebar: React.FC<BatchExplorationSidebarProps> = ({
  files = [],
  selectedFile,
  onFileSelect,
  onBackClick,
  activePanels = [],
  isLoading = false
}) => {
  const navigate = useNavigate();

  const handleBackClick = () => {
    if (onBackClick) {
      onBackClick();
    } else {
      navigate('/?tab=data');
    }
  };

  const handleFileChange = (fileId: string) => {
    const file = files.find(f => f.csv_id === fileId);
    if (file && onFileSelect) {
      onFileSelect(file);
    }
  };

  return (
    <div className="view-sidebar pl-0 pt-4 pb-4 pr-4 h-full flex flex-col shadow-none">
      <div className="sidebar-header mb-4">
        <Space direction="vertical" style={{ width: '100%' }}>
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={handleBackClick}
            style={{ padding: 0 }}
          >
            Back to Data Exploration
          </Button>

          {isLoading ? (
            <div style={{ textAlign: 'center', padding: '10px 0' }}>
              <Spin size="small" /> Loading files...
            </div>
          ) : (
            <Select
              placeholder="Select a file"
              style={{ width: '100%' }}
              value={selectedFile?.csv_id || undefined}
              onChange={handleFileChange}
              options={files.map(file => ({
                value: file.csv_id,
                label: file.file_name
              }))}
            />
          )}
        </Space>
      </div>

      {selectedFile && (
        <div className="sidebar-components flex-grow">
          <Collapse
            defaultActiveKey={['1']}
            ghost
            items={[
              {
                key: '1',
                label: 'Available Panels',
                style: {
                  borderRadius: 0,
                  backgroundColor: '#E9E9F5',
                  marginBottom: '15px',
                  border: '1px solid #b7b6b6'
                },
                children: (
                  <ul className="component-list">
                    <li
                      className={`component-item p-2 mb-2 rounded cursor-pointer ${
                        activePanels.includes(ComponentType.TimeSeriesPanel) 
                          ? 'opacity-50 cursor-not-allowed' 
                          : 'hover:bg-gray-100'
                      }`}
                      draggable={!activePanels.includes(ComponentType.TimeSeriesPanel)}
                      onDragStart={(e) => {
                        if (activePanels.includes(ComponentType.TimeSeriesPanel)) return;
                        e.dataTransfer.setData('component', ComponentType.TimeSeriesPanel);
                      }}
                    >
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Time Series Panel</span>
                        {activePanels.includes(ComponentType.TimeSeriesPanel) && (
                          <span className="text-xs text-gray-500">Added</span>
                        )}
                      </div>
                      <p className="text-xs text-gray-600 mt-1">
                        Visualize time-based data trends
                      </p>
                    </li>

                    <li
                      className={`component-item p-2 mb-2 rounded cursor-pointer ${
                        activePanels.includes(ComponentType.OverviewPanel) 
                          ? 'opacity-50 cursor-not-allowed' 
                          : 'hover:bg-gray-100'
                      }`}
                      draggable={!activePanels.includes(ComponentType.OverviewPanel)}
                      onDragStart={(e) => {
                        if (activePanels.includes(ComponentType.OverviewPanel)) return;
                        e.dataTransfer.setData('component', ComponentType.OverviewPanel);
                      }}
                    >
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Overview Panel</span>
                        {activePanels.includes(ComponentType.OverviewPanel) && (
                          <span className="text-xs text-gray-500">Added</span>
                        )}
                      </div>
                      <p className="text-xs text-gray-600 mt-1">
                        Display key metrics and statistics
                      </p>
                    </li>

                    <li
                      className={`component-item p-2 mb-2 rounded cursor-pointer ${
                        activePanels.includes(ComponentType.DataTablePanel) 
                          ? 'opacity-50 cursor-not-allowed' 
                          : 'hover:bg-gray-100'
                      }`}
                      draggable={!activePanels.includes(ComponentType.DataTablePanel)}
                      onDragStart={(e) => {
                        if (activePanels.includes(ComponentType.DataTablePanel)) return;
                        e.dataTransfer.setData('component', ComponentType.DataTablePanel);
                      }}
                    >
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Data Table Panel</span>
                        {activePanels.includes(ComponentType.DataTablePanel) && (
                          <span className="text-xs text-gray-500">Added</span>
                        )}
                      </div>
                      <p className="text-xs text-gray-600 mt-1">
                        Show data in tabular format
                      </p>
                    </li>
                  </ul>
                )
              }
            ]}
          />

          {/* Anomaly Listing Section */}
          <Collapse
            defaultActiveKey={['2']}
            ghost
            items={[
              {
                key: '2',
                label: 'Anomaly Detection',
                style: {
                  borderRadius: 0,
                  backgroundColor: '#FFF2E8',
                  marginBottom: '15px',
                  border: '1px solid #FFB366'
                },
                children: (
                  <div className="anomaly-section">
                    <p className="text-xs text-gray-600 mb-2">
                      Anomalies will be detected and listed here based on your data analysis.
                    </p>
                    <div className="text-center text-gray-400 py-4">
                      <span className="text-sm">No anomalies detected</span>
                    </div>
                  </div>
                )
              }
            ]}
          />
        </div>
      )}
    </div>
  );
};

export default BatchExplorationSidebar;
