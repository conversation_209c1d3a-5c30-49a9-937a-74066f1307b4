import React from 'react';
import { FileData, ComponentType } from '../Views Section/types';
import BatchExplorationSidebar from './BatchExplorationSidebar';
import PLCExplorationSidebar from './PLCExplorationSidebar';

interface DataExplorationSidebarProps {
  explorationType: 'batch' | 'plc';
  files?: FileData[];
  selectedFile?: FileData | null;
  onFileSelect?: (file: FileData) => void;
  onBackClick?: () => void;
  activePanels?: ComponentType[];
  isLoading?: boolean;
}

const DataExplorationSidebar: React.FC<DataExplorationSidebarProps> = ({
  explorationType,
  files,
  selectedFile,
  onFileSelect,
  onBackClick,
  activePanels,
  isLoading
}) => {
  if (explorationType === 'batch') {
    return (
      <BatchExplorationSidebar
        files={files}
        selectedFile={selectedFile}
        onFileSelect={onFileSelect}
        onBackClick={onBackClick}
        activePanels={activePanels}
        isLoading={isLoading}
      />
    );
  }

  if (explorationType === 'plc') {
    return (
      <PLCExplorationSidebar
        onBackClick={onBackClick}
      />
    );
  }

  return null;
};

export default DataExplorationSidebar;
