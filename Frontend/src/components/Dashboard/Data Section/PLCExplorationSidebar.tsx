import React from 'react';
import { But<PERSON>, <PERSON>, Collapse, Card } from 'antd';
import { ArrowLeftOutlined, DatabaseOutlined, SettingOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

interface PLCExplorationSidebarProps {
  onBackClick?: () => void;
}

const PLCExplorationSidebar: React.FC<PLCExplorationSidebarProps> = ({
  onBackClick
}) => {
  const navigate = useNavigate();

  const handleBackClick = () => {
    if (onBackClick) {
      onBackClick();
    } else {
      navigate('/?tab=data');
    }
  };

  return (
    <div className="view-sidebar pl-0 pt-4 pb-4 pr-4 h-full flex flex-col shadow-none">
      <div className="sidebar-header mb-4">
        <Space direction="vertical" style={{ width: '100%' }}>
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={handleBackClick}
            style={{ padding: 0 }}
          >
            Back to Data Exploration
          </Button>
        </Space>
      </div>

      <div className="sidebar-components flex-grow">
        <Collapse
          defaultActiveKey={['1']}
          ghost
          items={[
            {
              key: '1',
              label: 'PLC Data Sources',
              style: {
                borderRadius: 0,
                backgroundColor: '#E8F4FD',
                marginBottom: '15px',
                border: '1px solid #91D5FF'
              },
              children: (
                <div className="plc-sources">
                  <Card size="small" className="mb-2">
                    <div className="flex items-center space-x-2">
                      <DatabaseOutlined className="text-blue-500" />
                      <div>
                        <div className="text-sm font-medium">Production Line A</div>
                        <div className="text-xs text-gray-500">Status: Connected</div>
                      </div>
                    </div>
                  </Card>
                  
                  <Card size="small" className="mb-2">
                    <div className="flex items-center space-x-2">
                      <DatabaseOutlined className="text-green-500" />
                      <div>
                        <div className="text-sm font-medium">Production Line B</div>
                        <div className="text-xs text-gray-500">Status: Connected</div>
                      </div>
                    </div>
                  </Card>
                  
                  <Card size="small" className="mb-2">
                    <div className="flex items-center space-x-2">
                      <DatabaseOutlined className="text-orange-500" />
                      <div>
                        <div className="text-sm font-medium">Quality Control</div>
                        <div className="text-xs text-gray-500">Status: Offline</div>
                      </div>
                    </div>
                  </Card>
                </div>
              )
            }
          ]}
        />

        <Collapse
          defaultActiveKey={['2']}
          ghost
          items={[
            {
              key: '2',
              label: 'Real-time Monitoring',
              style: {
                borderRadius: 0,
                backgroundColor: '#F6FFED',
                marginBottom: '15px',
                border: '1px solid #B7EB8F'
              },
              children: (
                <div className="monitoring-options">
                  <div className="mb-3">
                    <div className="text-sm font-medium mb-1">Temperature Sensors</div>
                    <div className="text-xs text-gray-600">Monitor temperature across production lines</div>
                  </div>
                  
                  <div className="mb-3">
                    <div className="text-sm font-medium mb-1">Pressure Gauges</div>
                    <div className="text-xs text-gray-600">Track pressure variations in real-time</div>
                  </div>
                  
                  <div className="mb-3">
                    <div className="text-sm font-medium mb-1">Flow Meters</div>
                    <div className="text-xs text-gray-600">Monitor flow rates and volumes</div>
                  </div>
                  
                  <div className="mb-3">
                    <div className="text-sm font-medium mb-1">Motor Status</div>
                    <div className="text-xs text-gray-600">Check motor performance and health</div>
                  </div>
                </div>
              )
            }
          ]}
        />

        <Collapse
          defaultActiveKey={['3']}
          ghost
          items={[
            {
              key: '3',
              label: 'Configuration',
              style: {
                borderRadius: 0,
                backgroundColor: '#FFF1F0',
                marginBottom: '15px',
                border: '1px solid #FFCCC7'
              },
              children: (
                <div className="config-options">
                  <Button 
                    type="text" 
                    icon={<SettingOutlined />} 
                    className="w-full text-left mb-2"
                    size="small"
                  >
                    Connection Settings
                  </Button>
                  
                  <Button 
                    type="text" 
                    icon={<SettingOutlined />} 
                    className="w-full text-left mb-2"
                    size="small"
                  >
                    Data Refresh Rate
                  </Button>
                  
                  <Button 
                    type="text" 
                    icon={<SettingOutlined />} 
                    className="w-full text-left mb-2"
                    size="small"
                  >
                    Alert Thresholds
                  </Button>
                </div>
              )
            }
          ]}
        />

        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
          <div className="text-sm font-medium text-yellow-800 mb-1">
            Demo Mode
          </div>
          <div className="text-xs text-yellow-700">
            This is a demonstration of PLC data exploration features. 
            Connect your PLC systems to access real-time data.
          </div>
        </div>
      </div>
    </div>
  );
};

export default PLCExplorationSidebar;
