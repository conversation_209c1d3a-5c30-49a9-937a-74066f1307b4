import React, { useRef, useState } from 'react';
import { FileData, ComponentType, ColumnSelection, DateFilter } from '../Views Section/types';
import { PanelFilter } from '../Views Section/FilterTypes';
import GridLayout from '../Views Section/GridLayout';
import AppliedFilters from '../Views Section/AppliedFilters';
import { Tabs } from 'antd';
import '../Views Section/ViewStyles.css';

interface DataExplorationContentProps {
  explorationType: 'batch' | 'plc';
  selectedFile?: FileData | null;
  filteredData?: any;
  createInitialPanels?: boolean;
  onPanelsChange?: (panels: ComponentType[]) => void;
  selectedColumns?: ColumnSelection;
  dateFilter?: DateFilter;
  panelFilters?: Record<string, PanelFilter[]>;
  conditionalFilters?: any[];
  onColumnSelection?: (indices: number[], headers: string[]) => void;
  onDateFilterChange?: (startDate: string | null, endDate: string | null) => void;
  onZoomSelection?: (column: string, min: any, max: any, sourcePanelId?: string, zoomData?: any) => void;
  onClearAllFilters?: () => void;
  onAddFilter?: (filter: PanelFilter, panelId: string) => void;
  onRemoveFilter?: (filterId: string, panelId: string) => void;
  structure?: any;
}

const DataExplorationContent: React.FC<DataExplorationContentProps> = ({
  explorationType,
  selectedFile,
  filteredData,
  createInitialPanels = false,
  onPanelsChange,
  selectedColumns = { indices: [], headers: [] },
  dateFilter = { startDate: null, endDate: null },
  panelFilters = {},
  conditionalFilters = [],
  onColumnSelection,
  onDateFilterChange,
  onZoomSelection,
  onClearAllFilters,
  onAddFilter,
  onRemoveFilter,
  structure
}) => {
  const gridLayoutRef = useRef<{getLayout: () => any[], getItems: () => any[]}>(null);
  const [activeTab, setActiveTab] = useState(explorationType);

  const renderBatchExplorationContent = () => (
    <div className="view-content flex-1 p-4 h-full relative">
      {selectedFile ? (
        <div className="h-full">
          {/* Applied Filters Section */}
          <div className="flex flex-wrap items-center mb-4">
            <div className="flex-grow">
              <AppliedFilters
                selectedColumns={selectedColumns}
                dateFilter={dateFilter}
                conditionalFilters={conditionalFilters}
                onRemoveFilter={onRemoveFilter || (() => {})}
                onClearAllFilters={onClearAllFilters || (() => {})}
              />
            </div>
          </div>

          <GridLayout
            ref={gridLayoutRef}
            selectedFile={selectedFile}
            filteredData={filteredData}
            createInitialPanels={createInitialPanels}
            onPanelsChange={onPanelsChange}
            selectedColumns={selectedColumns}
            dateFilter={dateFilter}
            panelFilters={panelFilters}
            conditionalFilters={conditionalFilters}
            onColumnSelection={onColumnSelection}
            onDateFilterChange={onDateFilterChange}
            onZoomSelection={onZoomSelection}
            onAddFilter={onAddFilter}
            onRemoveFilter={onRemoveFilter}
            structure={structure}
          />
        </div>
      ) : (
        <div className="flex items-center justify-center h-full">
          <p className="text-gray-500">Select a file from the sidebar to start batch data exploration</p>
        </div>
      )}
    </div>
  );

  const renderPLCExplorationContent = () => (
    <div className="view-content flex-1 p-4 h-full relative">
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="text-6xl mb-4">🏭</div>
          <h3 className="text-xl font-semibold mb-2">PLC Data Exploration</h3>
          <p className="text-gray-500 mb-4">
            Real-time monitoring and analysis of PLC data streams
          </p>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-md mx-auto">
            <p className="text-sm text-blue-700">
              This feature is currently in development. Connect your PLC systems 
              to visualize real-time production data, monitor equipment status, 
              and detect anomalies in your manufacturing processes.
            </p>
          </div>
        </div>
      </div>
    </div>
  );

  const tabItems = [
    {
      key: 'batch',
      label: 'Batch Data Exploration',
      children: renderBatchExplorationContent(),
    },
    {
      key: 'plc',
      label: 'PLC Data Exploration',
      children: renderPLCExplorationContent(),
    },
  ];

  return (
    <div className="data-exploration-content h-full">
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
        className="h-full"
        tabBarStyle={{
          backgroundColor: '#F2F2F2',
          borderBottom: '1px solid #DEDEDE',
          margin: 0,
          paddingLeft: '16px'
        }}
      />
    </div>
  );
};

export default DataExplorationContent;
