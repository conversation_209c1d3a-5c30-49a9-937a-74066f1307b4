import { useRef, useEffect } from "react";
// import TabContent from "./TabContent";
import { ContextMenuAction, ContextMenuState, Tab } from "./Types/tabTypes";
import ContextMenu from './ContextMenu'; 



interface TabsContainerProps {
  tabs: Tab[];
  activeTabId: string | null;
  contextMenuState: ContextMenuState;
  onTabClick: (tabId: string) => void;
  onTabClose: (tabId: string) => void;
  onTabDragStart: (tabId: string) => void;
  onTabDrop: (sourceId: string, targetId: string) => void;
  onTabDragOver: (tabId: string) => void;
  onTabDragEnd: () => void;
  onContextMenu: (event: React.MouseEvent, tabId: string) => void;
  onContextMenuAction: (action: ContextMenuAction, tabId: string) => void;
  onContextMenuClose: () => void;
  onAddTab?: (fileType: 'ts' | 'js' | 'jsx' | 'tsx') => void;
}

export default function TabsContainer({
  tabs,
  activeTabId,
  contextMenuState,
  onTabClick,
  onTabClose,
  onTabDragStart,
  onTabDrop,
  onTabDragOver,
  onTabDragEnd,
  onContextMenu,
  onContextMenuAction,
  onContextMenuClose,
  onAddTab
}: TabsContainerProps) {
  const tabsContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Scroll the active tab into view
    if (activeTabId && tabsContainerRef.current) {
      const activeTab = tabsContainerRef.current.querySelector(`[data-tab-id="${activeTabId}"]`);
      if (activeTab) {
        activeTab.scrollIntoView({ behavior: "smooth", block: "nearest", inline: "nearest" });
      }
    }
  }, [activeTabId]);

  // Apply HTML5 drag and drop functionality to tabs
  const handleDragStart = (e: React.DragEvent<HTMLDivElement>, tabId: string) => {
    // Store the dragged tab's ID
    e.dataTransfer.setData('text/plain', tabId);
    // Add visual feedback
    e.currentTarget.classList.add('dragging');
    // Call parent handler
    onTabDragStart(tabId);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>, tabId: string) => {
    // Prevent default to allow drop
    e.preventDefault();
    
    // Clear any existing drag-over indicators
    const tabElements = tabsContainerRef.current?.querySelectorAll('.tab');
    tabElements?.forEach(tab => tab.classList.remove('drag-over'));
    
    // Add visual indicator on the current target
    e.currentTarget.classList.add('drag-over');
    
    // Call parent handler
    onTabDragOver(tabId);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>, targetTabId: string) => {
    e.preventDefault();
    
    // Remove visual indicators
    const tabElements = tabsContainerRef.current?.querySelectorAll('.tab');
    tabElements?.forEach(tab => {
      tab.classList.remove('drag-over');
      tab.classList.remove('dragging');
    });
    
    // Get the source tab ID from dataTransfer
    const sourceTabId = e.dataTransfer.getData('text/plain');
    
    // Only process if source and target are different
    if (sourceTabId !== targetTabId) {
      onTabDrop(sourceTabId, targetTabId);
    }
  };

  const handleDragEnd = (e: React.DragEvent<HTMLDivElement>) => {
    // Remove visual indicators
    const tabElements = tabsContainerRef.current?.querySelectorAll('.tab');
    tabElements?.forEach(tab => {
      tab.classList.remove('drag-over');
      tab.classList.remove('dragging');
    });
    
    // Call parent handler
    onTabDragEnd();
  };

  return (
    <>
      <div 
        ref={tabsContainerRef}
        id="tabsContainer"
        className="tab-container flex-1 flex overflow-x-auto no-scrollbar"
        style={{ scrollbarWidth: 'thin', scrollbarColor: '#555 #fff' }}
        onDragOver={(e) => {
          // Allow drag over the empty space in the tabs container
          e.preventDefault();
        }}
        onDrop={(e) => {
          // Handle dropping on empty space - append to the end
          e.preventDefault();
          
          // Clean up any visual indicators
          const tabElements = tabsContainerRef.current?.querySelectorAll('.tab');
          tabElements?.forEach(tab => {
            tab.classList.remove('drag-over');
            tab.classList.remove('dragging');
          });
          
          // Only process if we're not dropping on a tab
          if (!(e.target as HTMLElement).closest('.tab')) {
            const sourceTabId = e.dataTransfer.getData('text/plain');
            if (sourceTabId && tabs.length > 0) {
              // Use the last tab as the target for the drop
              const lastTabId = tabs[tabs.length - 1].id;
              onTabDrop(sourceTabId, lastTabId);
            }
          }
        }}
      >
        {/* {tabs.map((tab) => (
          <div
            key={tab.id}
            data-tab-id={tab.id}
            // make tab color 



            className={`tab relative flex items-center px-3 py-1 h-full group text-[13px] font-medium max-w-[200px] cursor-pointer border-r-2 border-[#ffffff]
              ${tab.id === activeTabId 
                ? 'bg-[#E6E6E6] text-[#4D4D4D] tab-active'    
                : 'bg-[#E6E6E6] text-[#4D4D4D]'
              }
            `}
            draggable={true}
            onDragStart={(e) => handleDragStart(e, tab.id)}
            onDragOver={(e) => handleDragOver(e, tab.id)}
            onDrop={(e) => handleDrop(e, tab.id)}
            onDragEnd={handleDragEnd}
            onClick={() => onTabClick(tab.id)}
            onContextMenu={(e) => onContextMenu(e, tab.id)}
          >
            {<span className="mr-2 text-[#252963]">VS</span>}

            <span className="truncate">{tab.label}</span>
            
            {tab.modified && <span className="h-2 w-2 rounded-full bg-white ml-1"></span>}
            
            <button
              className="ml-2 text-black"
              onClick={(e) => {
                e.stopPropagation();
                onTabClose(tab.id);
              }}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" viewBox="0 0 16 16">
                <path d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z"/>
              </svg>
            </button>
            
            {tab.id === activeTabId && (
              <div className="absolute bottom-0 left-0 w-full h-[2px] bg-[#252963]"></div>
            )}
          </div>
        ))} */}
      </div>
      
      {/* Extra Actions */}
      {/* <div className="flex-shrink-0 flex items-center px-2 space-x-2">
        {onAddTab && (
          <div className="relative group">
            <button 
              className="text-[#252963] p-1 focus:outline-none flex items-center"
              onClick={() => onAddTab('ts')}
              title="New Tab"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
              </svg>
            </button>
          </div>
        )}
        
        <button 
          className="text-[#252963] p-1 focus:outline-none"
          onClick={(e) => onContextMenu(e, activeTabId || '')}
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
            <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z"/>
          </svg>
        </button>
      </div> */}
      
      {/* Context Menu */}
      {contextMenuState.isVisible && (
        <ContextMenu
          x={contextMenuState.x}
          y={contextMenuState.y}
          tabId={contextMenuState.tabId}
          onAction={onContextMenuAction}
          onClose={onContextMenuClose}
        />
      )}
      
      {/* Tab Content */}
      {/* <div className="flex-1 overflow-auto">
        {activeTabId ? (
          <TabContent
            content={tabs.find(tab => tab.id === activeTabId)?.content || ''}
            fileType={tabs.find(tab => tab.id === activeTabId)?.icon || ''}
          />
        ) : (
          <div className="flex items-center justify-center h-full text-[#CCCCCC] opacity-50">
            No open tabs
          </div>
        )}
      </div> */}
    </>
  );
}
